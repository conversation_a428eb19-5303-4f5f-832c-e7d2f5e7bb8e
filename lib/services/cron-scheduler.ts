import cron, { ScheduledTask } from 'node-cron';
import { InvoiceAutomationService } from './invoice-automation';
import { StatsEmailService } from './stats-email-service';
import { StatsExportService } from './stats-export-service';
import { sendBrevoTemplatedEmail } from '@/lib/brevo/brevoService';
import { getBrevoSender } from '@/lib/brevo/brevoUtils';
import { GlobalEmailConfig } from '@/models/StatsEmailSettings';
import dbConnect from '@/lib/db';

export class CronJobError extends Error {
  constructor(
    message: string,
    public jobName: string,
    public originalError?: Error,
    public retryable: boolean = true
  ) {
    super(message);
    this.name = 'CronJobError';
  }
}
import { EventNotificationScheduler } from '../jobs/event-notification-scheduler';

export class CronScheduler {
  private static jobs: Map<string, ScheduledTask> = new Map();
  private static cronJobExecutionLogs: Map<string, any[]> = new Map();
  private static eventNotificationScheduler: EventNotificationScheduler = new EventNotificationScheduler();

  static startInvoiceAutomation() {
    console.log('Initializing invoice automation cron jobs...');

    // Tuesday 12:00 PM (0 12 * * 2)
    const tuesdayReminderJob = cron.schedule('0 12 * * 2', async () => {
      try {
        console.log('Tuesday 12pm cron job triggered');
        await InvoiceAutomationService.sendLateSigningReminders();
      } catch (error) {
        console.error('Tuesday reminder automation failed:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Thursday 12:00 PM (0 12 * * 4)
    const thursdayJob = cron.schedule('0 12 * * 4', async () => {
      try {
        console.log('Thursday 12pm cron job triggered');
        await InvoiceAutomationService.thursdayTransition();
      } catch (error) {
        console.error('Thursday automation failed:', error);
      }
    }, {
      timezone: 'America/Toronto' // Adjust to your local timezone
    });

    // Friday 12:00 PM (0 12 * * 5)
    const fridayJob = cron.schedule('0 12 * * 5', async () => {
      try {
        console.log('Friday 12pm cron job triggered');
        await InvoiceAutomationService.fridayTransition();
      } catch (error) {
        console.error('Friday automation failed:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Friday 23:59:59 (59 59 23 * * 5) - Archive all paid invoices
    const fridayArchiveJob = cron.schedule('59 59 23 * * 5', async () => {
      try {
        console.log('Friday 23:59:59 cron job triggered');
        await InvoiceAutomationService.fridayArchiveTransition();
      } catch (error) {
        console.error('Friday archive automation failed:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Every hour for late invoice check (0 * * * *) - DISABLED
    // Late invoice check is disabled - commenting out the job creation and start
    /*
    const lateCheckJob = cron.schedule('0 * * * *', async () => {
      try {
        console.log('Hourly late invoice check triggered');
        await InvoiceAutomationService.lateInvoiceTransition();
      } catch (error) {
        console.error('Late invoice check failed:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });
    */

    // Start jobs (excluding late check which is disabled)
    tuesdayReminderJob.start();
    thursdayJob.start();
    fridayJob.start();
    fridayArchiveJob.start();
    // lateCheckJob.start(); // DISABLED

    // Store references for management (excluding late check)
    this.jobs.set('tuesdayReminder', tuesdayReminderJob);
    this.jobs.set('thursday', thursdayJob);
    this.jobs.set('friday', fridayJob);
    this.jobs.set('fridayArchive', fridayArchiveJob);
    // this.jobs.set('lateCheck', lateCheckJob); // DISABLED

    console.log('Invoice automation cron jobs started successfully');
    console.log('- Tuesday 12pm: Late signing reminders');
    console.log('- Thursday 12pm: Signed → Processing');
    console.log('- Friday 12pm: Processing → Paid (with time condition)');
    console.log('- Friday 23:59:59: Archive all paid invoices');
    console.log('- Late invoice check: DISABLED (48h automatic transition disabled)');
  }

  static stopInvoiceAutomation() {
    console.log('Stopping invoice automation cron jobs...');

    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(`Stopped ${name} job`);
    });

    this.jobs.clear();
    console.log('All invoice automation jobs stopped');
  }

  static getJobStatus() {
    const status: Record<string, any> = {};

    this.jobs.forEach((_job, name) => {
      status[name] = {
        exists: true,
        // Note: node-cron ScheduledTask doesn't expose running/scheduled status
        // We track that the job exists in our Map
        name: name
      };
    });

    return {
      totalJobs: this.jobs.size,
      jobs: status,
      isActive: this.jobs.size > 0
    };
  }

  static restartInvoiceAutomation() {
    console.log('Restarting invoice automation...');
    this.stopInvoiceAutomation();
    this.startInvoiceAutomation();
  }

  // ===== DAILY STATS EMAIL AUTOMATION =====

  static async startDailyStatsEmailAutomation() {
    console.log('Initializing daily stats email cron jobs...');

    // Only run cron jobs in production or when explicitly enabled
    if (process.env.NODE_ENV === 'test' || process.env.DISABLE_CRON === 'true') {
      console.log('Daily stats email cron jobs disabled in test environment or by configuration');
      return;
    }

    let dailyStatsEmailJob: ScheduledTask;

    try {
      // Get the global email configuration to determine the sending hour
      await dbConnect();
      const globalConfig = await GlobalEmailConfig.getGlobalConfig();

      if (!globalConfig.isEnabled) {
        console.log('Daily stats email automation is disabled in global configuration');
        return;
      }

      const sendingHour = globalConfig.sendingHour;
      const sendingMinute = globalConfig.sendingMinute;
      const timezone = globalConfig.timezone;

      const timeString = `${sendingHour.toString().padStart(2, '0')}:${sendingMinute.toString().padStart(2, '0')}`;
      console.log(`Configuring daily stats email to send at ${timeString} ${timezone}`);

      // Daily job to send stats emails (configurable hour and minute every day)
      const cronExpression = `${sendingMinute} ${sendingHour} * * *`;
      dailyStatsEmailJob = cron.schedule(cronExpression, async () => {
        try {
          const currentTimeString = `${sendingHour.toString().padStart(2, '0')}:${sendingMinute.toString().padStart(2, '0')}`;
          console.log(`Running daily stats email job at ${currentTimeString}...`);
          await this.executeDailyStatsEmailJob();
        } catch (error) {
          console.error('Error in daily stats email cron job:', error);

          // Send alert email to admins about the failure
          await this.sendCronJobFailureAlert('daily-stats-email', error);
        }
      }, {
        timezone: timezone
      });
    } catch (error) {
      console.error('Error initializing daily stats email automation:', error);
      // Fall back to default 9 AM schedule if config loading fails
      dailyStatsEmailJob = cron.schedule('0 9 * * *', async () => {
        try {
          console.log('Running daily stats email job (fallback schedule)...');
          await this.executeDailyStatsEmailJob();
        } catch (error) {
          console.error('Error in daily stats email cron job:', error);
          await this.sendCronJobFailureAlert('daily-stats-email', error);
        }
      }, {
        timezone: 'America/Toronto'
      });
    }

    // Optional: Weekly summary job (Monday 9 AM) - placeholder for future (disabled)
    const weeklyStatsEmailJob = cron.schedule('0 9 * * 1', async () => {
      try {
        console.log('Running weekly stats email job...');
        await this.executeWeeklyStatsEmailJob();
      } catch (error) {
        console.error('Error in weekly stats email cron job:', error);
        await this.sendCronJobFailureAlert('weekly-stats-email', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Daily cleanup job for expired export tokens and files (2 AM every day)
    const exportCleanupJob = cron.schedule('0 2 * * *', async () => {
      try {
        console.log('Running export cleanup job...');
        await this.executeExportCleanupJob();
      } catch (error) {
        console.error('Error in export cleanup cron job:', error);
        await this.sendCronJobFailureAlert('export-cleanup', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Start daily job and cleanup job
    dailyStatsEmailJob.start();
    exportCleanupJob.start();
    // weeklyStatsEmailJob is disabled for now

    // Store job references for management
    this.jobs.set('dailyStatsEmail', dailyStatsEmailJob);
    this.jobs.set('weeklyStatsEmail', weeklyStatsEmailJob);
    this.jobs.set('exportCleanup', exportCleanupJob);

    try {
      const globalConfig = await GlobalEmailConfig.getGlobalConfig();
      const timeString = `${globalConfig.sendingHour.toString().padStart(2, '0')}:${globalConfig.sendingMinute.toString().padStart(2, '0')}`;
      console.log('Daily stats email cron jobs initialized successfully');
      console.log(`- Daily ${timeString} ${globalConfig.timezone}: Send daily statistics emails`);
      console.log('- Daily 2am: Cleanup expired export files and tokens');
      console.log('- Weekly Monday 9am: DISABLED (placeholder for future)');
    } catch (error) {
      console.log('Daily stats email cron jobs initialized successfully');
      console.log('- Daily 9:00 (fallback): Send daily statistics emails');
      console.log('- Daily 2am: Cleanup expired export files and tokens');
      console.log('- Weekly Monday 9am: DISABLED (placeholder for future)');
    }
  }

  static stopDailyStatsEmailAutomation() {
    console.log('Stopping daily stats email cron jobs...');

    const statsEmailJobs = ['dailyStatsEmail', 'weeklyStatsEmail', 'exportCleanup'];

    statsEmailJobs.forEach(jobName => {
      const job = this.jobs.get(jobName);
      if (job) {
        job.stop();
        this.jobs.delete(jobName);
        console.log(`Stopped ${jobName} job`);
      }
    });

    console.log('All daily stats email jobs stopped');
  }

  static async restartDailyStatsEmailAutomation() {
    console.log('Restarting daily stats email automation...');
    this.stopDailyStatsEmailAutomation();
    await this.startDailyStatsEmailAutomation();
  }

  // ===== WEEKLY STATS EMAIL AUTOMATION =====

  static async startWeeklyStatsEmailAutomation() {
    console.log('Initializing weekly stats email cron jobs...');

    // Only run cron jobs in production or when explicitly enabled
    if (process.env.NODE_ENV === 'test' || process.env.DISABLE_CRON === 'true') {
      console.log('Weekly stats email cron jobs disabled in test environment or by configuration');
      return;
    }

    try {
      await dbConnect();
      const globalConfig = await GlobalEmailConfig.getGlobalConfig();

      if (!globalConfig.weeklyEnabled) {
        console.log('Weekly stats emails are disabled in global configuration');
        return;
      }

      // Create cron expression for weekly schedule
      const cronExpression = `${globalConfig.weeklyMinute} ${globalConfig.weeklyHour} * * ${globalConfig.weeklyDay}`;
      console.log(`Setting up weekly stats email job with cron expression: ${cronExpression} (timezone: ${globalConfig.weeklyTimezone})`);

      const weeklyJob = cron.schedule(cronExpression, async () => {
        await this.executeWeeklyStatsEmailJob();
      }, {
        timezone: globalConfig.weeklyTimezone
      });

      this.jobs.set('weekly-stats-email', weeklyJob);

      console.log(`Weekly stats email automation started - will run on day ${globalConfig.weeklyDay} at ${globalConfig.weeklyHour}:${globalConfig.weeklyMinute.toString().padStart(2, '0')} ${globalConfig.weeklyTimezone}`);

    } catch (error) {
      console.error('Failed to start weekly stats email automation:', error);
      throw new CronJobError(
        'Failed to initialize weekly stats email automation',
        'weekly-stats-email-init',
        error instanceof Error ? error : new Error(String(error)),
        true
      );
    }
  }

  static stopWeeklyStatsEmailAutomation() {
    console.log('Stopping weekly stats email automation...');

    const weeklyJobNames = ['weekly-stats-email'];

    weeklyJobNames.forEach(jobName => {
      const job = this.jobs.get(jobName);
      if (job) {
        job.stop();
        job.destroy();
        this.jobs.delete(jobName);
        console.log(`Stopped ${jobName} job`);
      }
    });

    console.log('All weekly stats email jobs stopped');
  }

  static async restartWeeklyStatsEmailAutomation() {
    console.log('Restarting weekly stats email automation...');
    this.stopWeeklyStatsEmailAutomation();
    await this.startWeeklyStatsEmailAutomation();
  }

  // ===== WEEKLY STATS EMAIL JOB EXECUTION =====

  // ===== DAILY STATS EMAIL JOB EXECUTION =====

  private static async executeDailyStatsEmailJob(): Promise<void> {
    await this.executeJobWithErrorHandling(
      'daily-stats-email',
      async () => {
        const statsEmailService = StatsEmailService.getInstance();

        // Perform health check first
        const health = await statsEmailService.healthCheck();

        if (health.status === 'unhealthy') {
          throw new CronJobError(
            `Service unhealthy: ${health.errors.join(', ')}`,
            'daily-stats-email',
            undefined,
            true // Retryable
          );
        }

        // Execute with retry logic
        const result = await statsEmailService.sendDailyStatsEmailsWithRetry();

        // Log success metrics
        await this.logCronJobExecution('daily-stats-email', {
          success: true,
          totalEmails: result.totalEmails,
          successCount: result.successCount,
          failureCount: result.failureCount,
          executionTime: result.executionTime,
          healthStatus: health.status
        });

        return result;
      },
      3 // Max 3 retries for the entire job
    );
  }

  private static async executeWeeklyStatsEmailJob(): Promise<void> {
    await this.executeJobWithErrorHandling(
      'weekly-stats-email',
      async () => {
        const statsEmailService = StatsEmailService.getInstance();

        // Get global config to determine the weekly day
        await dbConnect();
        const globalConfig = await GlobalEmailConfig.getGlobalConfig();

        if (!globalConfig.weeklyEnabled) {
          console.log('Weekly stats emails are disabled, skipping job execution');
          return;
        }

        console.log(`Executing weekly stats email job for day ${globalConfig.weeklyDay}`);

        const result = await statsEmailService.sendWeeklyStatsEmails(globalConfig.weeklyDay);

        console.log(`Weekly stats email job completed:`, {
          totalEmails: result.totalEmails,
          successCount: result.successCount,
          failureCount: result.failureCount,
          executionTime: result.executionTime
        });

        if (result.errors.length > 0) {
          console.warn('Weekly stats email job had errors:', result.errors);
        }
      }
    );
  }

  private static async executeExportCleanupJob(): Promise<void> {
    await this.executeJobWithErrorHandling(
      'export-cleanup',
      async () => {
        const exportService = StatsExportService.getInstance();

        console.log('Starting export cleanup...');
        await exportService.cleanupExpiredFiles();

        // Log success
        await this.logCronJobExecution('export-cleanup', {
          success: true,
          message: 'Export cleanup completed successfully'
        });

        console.log('Export cleanup completed successfully');
      },
      2 // Max 2 retries for cleanup job
    );
  }

  // ===== ERROR HANDLING AND RETRY MECHANISMS =====

  private static async executeJobWithErrorHandling<T>(
    jobName: string,
    jobFunction: () => Promise<T>,
    maxRetries: number = 2
  ): Promise<T> {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
      try {
        const result = await jobFunction();

        if (attempt > 1) {
          console.log(`Job ${jobName} succeeded on attempt ${attempt}`);
        }

        return result;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');

        // Determine if error is retryable
        const isRetryable = this.isRetryableError(error);

        if (!isRetryable || attempt > maxRetries) {
          console.error(`Job ${jobName} failed permanently:`, lastError.message);
          throw new CronJobError(
            `Job ${jobName} failed after ${attempt} attempts`,
            jobName,
            lastError,
            isRetryable
          );
        }

        console.warn(`Job ${jobName} attempt ${attempt} failed (retryable):`, lastError.message);

        // Wait before retry (exponential backoff)
        const delay = Math.min(Math.pow(2, attempt - 1) * 1000, 30000); // Max 30 seconds
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw lastError!; // This should never be reached
  }

  private static isRetryableError(error: any): boolean {
    if (error instanceof CronJobError) {
      return error.retryable;
    }

    // Network-related errors are usually retryable
    if (error.code === 'ECONNREFUSED' ||
        error.code === 'ENOTFOUND' ||
        error.code === 'ETIMEDOUT') {
      return true;
    }

    // Database connection errors are retryable
    if (error.message?.includes('connection') ||
        error.message?.includes('timeout')) {
      return true;
    }

    // Email service errors might be retryable
    if (error.message?.includes('rate limit') ||
        error.message?.includes('temporary')) {
      return true;
    }

    // Authentication errors are usually not retryable
    if (error.message?.includes('authentication') ||
        error.message?.includes('unauthorized')) {
      return false;
    }

    // Default to retryable for unknown errors
    return true;
  }

  // ===== CRON JOB MONITORING AND LOGGING =====

  public static async logCronJobExecution(jobName: string, metrics: any): Promise<void> {
    const logEntry = {
      timestamp: new Date(),
      jobName,
      ...metrics
    };

    // Store in memory (consider database storage for production)
    if (!this.cronJobExecutionLogs.has(jobName)) {
      this.cronJobExecutionLogs.set(jobName, []);
    }

    const logs = this.cronJobExecutionLogs.get(jobName)!;
    logs.unshift(logEntry);

    // Keep only last 100 executions
    if (logs.length > 100) {
      logs.splice(100);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`[CronJobLog] ${jobName}:`, logEntry);
    }
  }

  public static getCronJobLogs(jobName: string, limit: number = 10): any[] {
    const logs = this.cronJobExecutionLogs.get(jobName) || [];
    return logs.slice(0, limit);
  }

  public static getCronJobStats(jobName: string): {
    totalExecutions: number;
    successRate: number;
    averageExecutionTime: number;
    lastExecution: Date | null;
    recentErrors: string[];
  } {
    const logs = this.cronJobExecutionLogs.get(jobName) || [];

    if (logs.length === 0) {
      return {
        totalExecutions: 0,
        successRate: 0,
        averageExecutionTime: 0,
        lastExecution: null,
        recentErrors: []
      };
    }

    const successful = logs.filter(log => log.success).length;
    const avgTime = logs.reduce((sum, log) => sum + (log.executionTime || 0), 0) / logs.length;
    const recentErrors = logs
      .filter(log => !log.success && log.error)
      .slice(0, 5)
      .map(log => log.error);

    return {
      totalExecutions: logs.length,
      successRate: (successful / logs.length) * 100,
      averageExecutionTime: avgTime,
      lastExecution: logs[0]?.timestamp || null,
      recentErrors
    };
  }

  // ===== FAILURE ALERT SYSTEM =====

  private static async sendCronJobFailureAlert(jobName: string, error: any): Promise<void> {
    try {
      const alertEmail = process.env.CRON_ALERT_EMAIL || process.env.MAIL_USER;

      if (!alertEmail) {
        console.warn('No alert email configured for cron job failures');
        return;
      }

      const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
      const timestamp = new Date().toISOString();

      const alertContent = `
        <h2>Cron Job Failure Alert</h2>
        <p><strong>Job Name:</strong> ${jobName}</p>
        <p><strong>Timestamp:</strong> ${timestamp}</p>
        <p><strong>Error:</strong> ${errorMessage}</p>
        <p><strong>Server:</strong> ${process.env.NODE_ENV || 'unknown'}</p>

        <h3>Recent Job Statistics</h3>
        <pre>${JSON.stringify(this.getCronJobStats(jobName), null, 2)}</pre>
      `;

      // Use existing email service to send alert
      const brevoSender = getBrevoSender();
      await sendBrevoTemplatedEmail({
        to: [{ email: alertEmail }],
        subject: `[ALERT] Cron Job Failure: ${jobName}`,
        content: alertContent,
        sender: {
          email: brevoSender.email,
          name: 'AMQ Partners - System Alerts'
        }
      });

      console.log(`Failure alert sent for cron job: ${jobName}`);
    } catch (alertError) {
      console.error('Failed to send cron job failure alert:', alertError);
    }
  }

  // Manual trigger methods for testing
  static async triggerTuesdayReminderAutomation() {
    console.log('Manually triggering Tuesday late signing reminder automation...');
    return await InvoiceAutomationService.sendLateSigningReminders();
  }

  static async triggerThursdayAutomation() {
    console.log('Manually triggering Thursday automation...');
    return await InvoiceAutomationService.thursdayTransition();
  }

  static async triggerFridayAutomation() {
    console.log('Manually triggering Friday automation...');
    return await InvoiceAutomationService.fridayTransition();
  }

  static async triggerLateInvoiceCheck() {
    console.log('Late invoice check is disabled - manual trigger skipped');
    return await InvoiceAutomationService.lateInvoiceTransition();
  }

  static async triggerFridayArchiveAutomation() {
    console.log('Manually triggering Friday archive automation...');
    return await InvoiceAutomationService.fridayArchiveTransition();
  }

  // ===== MANUAL JOB EXECUTION (for testing) =====

  public static async executeExportCleanupManually(): Promise<any> {
    const startTime = Date.now();
    console.log('Manually executing export cleanup job...');

    try {
      const exportService = StatsExportService.getInstance();
      await exportService.cleanupExpiredFiles();

      const executionTime = Date.now() - startTime;

      // Log the success
      await this.logCronJobExecution('export-cleanup', {
        success: true,
        executionTime,
        manual: true,
        message: 'Manual export cleanup completed successfully'
      });

      return {
        success: true,
        executionTime,
        message: 'Export cleanup completed successfully'
      };
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      // Log the failure
      await this.logCronJobExecution('export-cleanup', {
        success: false,
        executionTime,
        error: errorMessage,
        manual: true
      });

      throw error;
    }
  }

  public static async executeDailyStatsEmailManually(date?: string): Promise<any> {
    const startTime = Date.now();
    console.log('Manually executing daily stats email job...');

    try {
      const statsEmailService = StatsEmailService.getInstance();

      // Perform health check first
      const health = await statsEmailService.healthCheck();

      if (health.status === 'unhealthy') {
        const error = new Error(`Service unhealthy: ${health.errors.join(', ')}`);
        const executionTime = Date.now() - startTime;

        // Log the failure
        await this.logCronJobExecution('daily-stats-email', {
          success: false,
          executionTime,
          error: error.message,
          healthStatus: health.status,
          manual: true
        });

        throw error;
      }

      // Execute with retry logic
      const result = await statsEmailService.sendDailyStatsEmailsWithRetry(date);
      const executionTime = Date.now() - startTime;

      // Log success metrics
      await this.logCronJobExecution('daily-stats-email', {
        success: true,
        totalEmails: result.totalEmails,
        successCount: result.successCount,
        failureCount: result.failureCount,
        executionTime,
        healthStatus: health.status,
        manual: true
      });

      console.log('Manual execution completed:', result);
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;

      // Log the failure
      await this.logCronJobExecution('daily-stats-email', {
        success: false,
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        manual: true
      });

      console.error('Manual execution failed:', error);
      throw error;
    }
  }

  // ===== EVENT NOTIFICATION AUTOMATION =====

  static startEventNotificationAutomation() {
    console.log('Initializing event notification cron jobs...');

    // Only run cron jobs in production or when explicitly enabled
    if (process.env.NODE_ENV === 'test' || process.env.DISABLE_CRON === 'true') {
      console.log('Event notification cron jobs disabled in test environment or by configuration');
      return;
    }

    // Daily job to check for overdue reports (9 AM every day)
    const overdueReportsJob = cron.schedule('0 9 * * *', async () => {
      try {
        console.log('Running overdue reports check...');
        await this.eventNotificationScheduler.checkOverdueReports();
      } catch (error) {
        console.error('Error in overdue reports cron job:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Daily job to check for upcoming events (8 AM every day)
    const upcomingEventsJob = cron.schedule('0 8 * * *', async () => {
      try {
        console.log('Running upcoming events check...');
        await this.eventNotificationScheduler.checkUpcomingEvents();
      } catch (error) {
        console.error('Error in upcoming events cron job:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Job to check for validation deadlines (every 4 hours)
    const validationDeadlinesJob = cron.schedule('0 */4 * * *', async () => {
      try {
        console.log('Running validation deadlines check...');
        await this.eventNotificationScheduler.checkValidationDeadlines();
      } catch (error) {
        console.error('Error in validation deadlines cron job:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Hourly job to check for event status transitions
    const eventStatusTransitionsJob = cron.schedule('0 * * * *', async () => {
      try {
        console.log('Running event status transitions check...');
        await this.eventNotificationScheduler.checkEventStatusTransitions();
      } catch (error) {
        console.error('Error in event status transitions cron job:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Daily job to check for commission calculation opportunities (10 AM every day)
    const commissionCalculationJob = cron.schedule('0 10 * * *', async () => {
      try {
        console.log('Running commission calculation opportunities check...');
        await this.eventNotificationScheduler.checkCommissionCalculationOpportunities();
      } catch (error) {
        console.error('Error in commission calculation cron job:', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Start all event notification jobs
    overdueReportsJob.start();
    upcomingEventsJob.start();
    validationDeadlinesJob.start();
    eventStatusTransitionsJob.start();
    commissionCalculationJob.start();

    // Store references for management
    this.jobs.set('eventOverdueReports', overdueReportsJob);
    this.jobs.set('eventUpcomingEvents', upcomingEventsJob);
    this.jobs.set('eventValidationDeadlines', validationDeadlinesJob);
    this.jobs.set('eventStatusTransitions', eventStatusTransitionsJob);
    this.jobs.set('eventCommissionCalculation', commissionCalculationJob);

    console.log('Event notification cron jobs started successfully');
    console.log('- Daily 8am: Upcoming event reminders');
    console.log('- Daily 9am: Overdue report notifications');
    console.log('- Daily 10am: Commission calculation opportunities');
    console.log('- Every 4 hours: Validation deadline reminders');
    console.log('- Hourly: Event status transitions');
  }

  static stopEventNotificationAutomation() {
    console.log('Stopping event notification cron jobs...');

    const eventJobs = ['eventOverdueReports', 'eventUpcomingEvents', 'eventValidationDeadlines',
                      'eventStatusTransitions', 'eventCommissionCalculation'];

    eventJobs.forEach(jobName => {
      const job = this.jobs.get(jobName);
      if (job) {
        job.stop();
        this.jobs.delete(jobName);
        console.log(`Stopped ${jobName} job`);
      }
    });

    console.log('All event notification jobs stopped');
  }

  static restartEventNotificationAutomation() {
    console.log('Restarting event notification automation...');
    this.stopEventNotificationAutomation();
    this.startEventNotificationAutomation();
  }

  // Manual trigger methods for event notifications
  static async triggerOverdueReportsCheck() {
    console.log('Manually triggering overdue reports check...');
    return await this.eventNotificationScheduler.checkOverdueReports();
  }

  static async triggerUpcomingEventsCheck() {
    console.log('Manually triggering upcoming events check...');
    return await this.eventNotificationScheduler.checkUpcomingEvents();
  }

  static async triggerValidationDeadlinesCheck() {
    console.log('Manually triggering validation deadlines check...');
    return await this.eventNotificationScheduler.checkValidationDeadlines();
  }

  static async triggerEventStatusTransitions() {
    console.log('Manually triggering event status transitions...');
    return await this.eventNotificationScheduler.checkEventStatusTransitions();
  }

  static async triggerCommissionCalculationCheck() {
    console.log('Manually triggering commission calculation check...');
    return await this.eventNotificationScheduler.checkCommissionCalculationOpportunities();
  }

  static async triggerAllEventNotifications() {
    console.log('Manually triggering all event notification checks...');
    return await this.eventNotificationScheduler.runAllChecks();
  }

  // Get event notification statistics
  static async getEventNotificationStats() {
    return await this.eventNotificationScheduler.getNotificationStats();
  }

  // Utility method to validate cron expressions
  static validateCronExpression(expression: string): boolean {
    return cron.validate(expression);
  }

  // Get next scheduled run times
  static getNextRunTimes() {
    const nextRuns: Record<string, string | null> = {};

    this.jobs.forEach((_job, name) => {
      try {
        // Note: node-cron doesn't provide a direct way to get next run time
        // This is a simplified approach
        const now = new Date();
        switch (name) {
          case 'thursday':
            nextRuns[name] = getNextThursday12pm().toISOString();
            break;
          case 'friday':
            nextRuns[name] = getNextFriday12pm().toISOString();
            break;
          case 'fridayArchive':
            nextRuns[name] = getNextFriday2359().toISOString();
            break;
          case 'dailyStatsEmail':
            nextRuns[name] = getNextDaily8am().toISOString();
            break;
          case 'exportCleanup':
            nextRuns[name] = getNextDaily2am().toISOString();
            break;
          case 'weeklyStatsEmail':
            nextRuns[name] = getNextMonday9am().toISOString();
            break;
          case 'lateCheck':
            const nextHour = new Date(now);
            nextHour.setHours(nextHour.getHours() + 1, 0, 0, 0);
            nextRuns[name] = nextHour.toISOString();
            break;
          default:
            nextRuns[name] = null;
        }
      } catch (error) {
        nextRuns[name] = null;
      }
    });

    return nextRuns;
  }

  // ===== BRANCH STATS EMAIL AUTOMATION =====

  /**
   * Start branch stats email automation
   */
  static async startBranchStatsEmailAutomation() {
    console.log('Initializing branch stats email automation...');

    // Only run cron jobs in production or when explicitly enabled
    if (process.env.NODE_ENV === 'test' || process.env.DISABLE_CRON === 'true') {
      console.log('Branch stats email automation disabled in test environment or by configuration');
      return;
    }

    let branchStatsEmailJob: ScheduledTask;

    try {
      await dbConnect();

      // Get global configuration for branch stats
      const globalConfig = await GlobalEmailConfig.getGlobalConfig();

      if (!globalConfig.branchStatsEnabled) {
        console.log('Branch stats emails are disabled in global configuration');
        return;
      }

      const { branchStatsHour, branchStatsMinute, branchStatsTimezone } = globalConfig;

      // Create cron expression for branch stats (minute hour * * *)
      const cronExpression = `${branchStatsMinute} ${branchStatsHour} * * *`;

      console.log(`Setting up branch stats email job with cron expression: ${cronExpression} (timezone: ${branchStatsTimezone})`);

      branchStatsEmailJob = cron.schedule(cronExpression, async () => {
        try {
          const currentTimeString = `${branchStatsHour.toString().padStart(2, '0')}:${branchStatsMinute.toString().padStart(2, '0')}`;
          console.log(`Running branch stats email job at ${currentTimeString}...`);
          await this.executeBranchStatsEmailJob();
        } catch (error) {
          console.error('Error in branch stats email cron job:', error);

          // Send alert email to admins about the failure
          await this.sendCronJobFailureAlert('branch-stats-email', error);
        }
      }, {
        timezone: branchStatsTimezone
      });

    } catch (error) {
      console.error('Error initializing branch stats email automation:', error);

      // Fall back to default 10 AM schedule if config loading fails
      branchStatsEmailJob = cron.schedule('0 10 * * *', async () => {
        try {
          console.log('Running branch stats email job (fallback schedule)...');
          await this.executeBranchStatsEmailJob();
        } catch (error) {
          console.error('Error in branch stats email cron job:', error);
          await this.sendCronJobFailureAlert('branch-stats-email', error);
        }
      }, {
        timezone: 'America/Toronto'
      });
    }

    // Start the job
    branchStatsEmailJob.start();

    // Store job reference for management
    this.jobs.set('branchStatsEmail', branchStatsEmailJob);

    console.log('Branch stats email automation initialized successfully');
  }

  /**
   * Execute branch stats email job
   */
  static async executeBranchStatsEmailJob(): Promise<void> {
    const startTime = Date.now();

    try {
      console.log('Executing branch stats email job...');

      // Import service dynamically to avoid circular dependencies
      const { BranchStatsEmailService } = await import('./branch-stats-email-service');
      const branchStatsEmailService = new BranchStatsEmailService();

      // Send branch stats emails (service will calculate target date based on current time)
      const result = await branchStatsEmailService.sendBranchStatsEmails();

      const executionTime = Date.now() - startTime;

      // Log execution results
      this.logCronJobExecution('branch-stats-email', {
        success: true,
        executionTime,
        totalEmails: result.totalEmails,
        successCount: result.successCount,
        failureCount: result.failureCount,
        errors: result.errors,
        timestamp: new Date()
      });

      console.log(`Branch stats email job completed successfully in ${executionTime}ms`);
      console.log(`Emails sent: ${result.successCount}/${result.totalEmails} successful`);

      if (result.failureCount > 0) {
        console.warn(`Branch stats email job had ${result.failureCount} failures:`, result.errors);
      }

    } catch (error) {
      const executionTime = Date.now() - startTime;

      console.error('Branch stats email job failed:', error);

      // Log execution failure
      this.logCronJobExecution('branch-stats-email', {
        success: false,
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date()
      });

      throw error; // Re-throw to trigger failure alert
    }
  }

  /**
   * Restart branch stats email automation with new configuration
   */
  static async restartBranchStatsEmailAutomation(): Promise<void> {
    console.log('Restarting branch stats email automation...');

    try {
      // Stop existing job if it exists
      const existingJob = this.jobs.get('branchStatsEmail');
      if (existingJob) {
        existingJob.stop();
        existingJob.destroy();
        this.jobs.delete('branchStatsEmail');
        console.log('Stopped existing branch stats email job');
      }

      // Start new job with updated configuration
      await this.startBranchStatsEmailAutomation();

      console.log('Branch stats email automation restarted successfully');
    } catch (error) {
      console.error('Error restarting branch stats email automation:', error);
      throw error;
    }
  }

  /**
   * Manual execution of branch stats email job (for testing/debugging)
   */
  static async executeBranchStatsEmailJobManually(targetDate?: string): Promise<any> {
    console.log('Manually executing branch stats email job...');

    try {
      const { BranchStatsEmailService } = await import('./branch-stats-email-service');
      const branchStatsEmailService = new BranchStatsEmailService();

      const result = await branchStatsEmailService.sendBranchStatsEmails(targetDate);

      // Log manual execution
      this.logCronJobExecution('branch-stats-email-manual', {
        success: true,
        executionTime: result.executionTime || 0,
        totalEmails: result.totalEmails,
        successCount: result.successCount,
        failureCount: result.failureCount,
        errors: result.errors,
        timestamp: new Date(),
        manual: true,
        targetDate
      });

      return result;
    } catch (error) {
      console.error('Manual branch stats email job failed:', error);

      // Log manual execution failure
      this.logCronJobExecution('branch-stats-email-manual', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        manual: true,
        targetDate
      });

      throw error;
    }
  }

  // ===== MONDAY RESERVATION REMINDER AUTOMATION =====

  // Configuration: Branch IDs that should receive Monday reservation reminders
  private static MONDAY_REMINDER_BRANCH_IDS: string[] = [];

  // Load Monday reminder configuration from config file
  private static async loadMondayReminderConfiguration(): Promise<void> {
    try {
      const { MONDAY_REMINDER_BRANCH_IDS } = await import('@/lib/config/monday-reminder-branches');
      this.MONDAY_REMINDER_BRANCH_IDS = [...MONDAY_REMINDER_BRANCH_IDS];
      console.log(`Loaded ${this.MONDAY_REMINDER_BRANCH_IDS.length} branch IDs for Monday reminders`);
    } catch (error) {
      console.warn('Could not load Monday reminder configuration, using empty array:', error);
      this.MONDAY_REMINDER_BRANCH_IDS = [];
    }
  }

  static async startMondayReservationReminderAutomation() {
    console.log('Initializing Monday reservation reminder cron jobs...');

    // Only run cron jobs in production or when explicitly enabled
    if (process.env.NODE_ENV === 'test' || process.env.DISABLE_CRON === 'true') {
      console.log('Monday reservation reminder cron jobs disabled in test environment or by configuration');
      return;
    }

    // Load branch IDs from configuration
    await this.loadMondayReminderConfiguration();

    // Saturday 7:00 PM (0 19 * * 6) - Send reminders for Monday reservations
    const mondayReminderJob = cron.schedule('0 19 * * 6', async () => {
      try {
        console.log('Saturday 7pm cron job triggered - checking Monday reservations for reminders');
        await this.executeMondayReservationReminderJob();
      } catch (error) {
        console.error('Monday reservation reminder automation failed:', error);
        await this.sendCronJobFailureAlert('monday-reservation-reminder', error);
      }
    }, {
      timezone: 'America/Toronto'
    });

    // Start the job
    mondayReminderJob.start();

    // Store reference for management
    this.jobs.set('mondayReservationReminder', mondayReminderJob);

    console.log('Monday reservation reminder cron job started successfully');
    console.log('- Saturday 7pm: Send reminders for Monday reservations (48h before)');
    console.log(`- Configured for ${this.MONDAY_REMINDER_BRANCH_IDS.length} branches`);
  }

  static stopMondayReservationReminderAutomation() {
    console.log('Stopping Monday reservation reminder cron jobs...');

    const job = this.jobs.get('mondayReservationReminder');
    if (job) {
      job.stop();
      this.jobs.delete('mondayReservationReminder');
      console.log('Stopped mondayReservationReminder job');
    }

    console.log('Monday reservation reminder jobs stopped');
  }

  static restartMondayReservationReminderAutomation() {
    console.log('Restarting Monday reservation reminder automation...');
    this.stopMondayReservationReminderAutomation();
    this.startMondayReservationReminderAutomation();
  }

  // ===== MONDAY RESERVATION REMINDER JOB EXECUTION =====

  private static async executeMondayReservationReminderJob(): Promise<void> {
    await this.executeJobWithErrorHandling(
      'monday-reservation-reminder',
      async () => {
        const startTime = Date.now();
        console.log('Executing Monday reservation reminder job...');

        // Skip if no branches configured
        if (this.MONDAY_REMINDER_BRANCH_IDS.length === 0) {
          console.log('No branches configured for Monday reservation reminders');
          return { totalReservations: 0, smsScheduled: 0, skipped: 0 };
        }

        // Calculate next Monday's date
        const nextMonday = this.getNextMondayDate();
        const nextMondayStr = nextMonday.toISOString().split('T')[0]; // YYYY-MM-DD format

        console.log(`Looking for reservations on ${nextMondayStr} for branches: ${this.MONDAY_REMINDER_BRANCH_IDS.join(', ')}`);

        // Import models dynamically to avoid circular dependencies
        const Reservation = (await import('@/models/Reservation')).default;
        const ScheduledSMS = (await import('@/models/ScheduledSMS')).default;
        const { processTemplate } = await import('@/lib/sms-templates');

        await dbConnect();

        // Find reservations for next Monday in specified branches
        const reservations = await Reservation.find({
          'preferences.visitDate': nextMondayStr,
          'preferences.branchId': { $in: this.MONDAY_REMINDER_BRANCH_IDS },
          isDeleted: { $ne: true },
          status: { $nin: ['cancelled', 'absent'] } // Exclude cancelled/absent reservations
        }).populate('appointmentId');

        console.log(`Found ${reservations.length} reservations for Monday ${nextMondayStr}`);

        let smsScheduled = 0;
        let skipped = 0;
        const errors: string[] = [];

        for (const reservation of reservations) {
          try {
            // Check if reservation was created more than 1 day ago
            const createdAt = new Date(reservation.createdAt);
            const oneDayAgo = new Date();
            oneDayAgo.setDate(oneDayAgo.getDate() - 1);

            if (createdAt > oneDayAgo) {
              console.log(`Skipping reservation ${reservation._id} - created less than 1 day ago`);
              skipped++;
              continue;
            }

            // Get branch information
            const branchData = await this.getBranchData(reservation.preferences.branchId);
            if (!branchData) {
              console.warn(`Branch not found for reservation ${reservation._id}`);
              skipped++;
              continue;
            }

            // Prepare SMS data
            const smsData = await this.prepareMondayReminderSMSData(reservation, branchData);

            // Create SMS message
            const smsTemplate = `Bonjour {{customerInfo.client1Name}},
Petit rappel de votre expérience VIP – dégustation 5 services
📅 Le {{formattedDate}} à {{visitHour}}
📍 {{branch.address}}

🎁 Votre présence vous rend admissible au tirage hebdomadaire de 2 500 $ en produits!
🧊 Et vous repartez avec 25 $ de produits, même sans achat.

✅ Merci de confirmer votre présence.
À bientôt!
— Équipe AMQ`;

            const smsBody = processTemplate(smsTemplate, smsData);

            // Schedule SMS to be sent immediately (since we're already at the right time)
            const scheduledAt = new Date();

            await ScheduledSMS.create({
              templateId: null, // No specific template ID for this custom message
              body: smsBody,
              reservationId: reservation._id,
              status: 'pending',
              scheduledAt: scheduledAt,
              createdAt: new Date()
            });

            console.log(`Scheduled Monday reminder SMS for reservation ${reservation._id}`);
            smsScheduled++;

          } catch (error) {
            const errorMsg = `Error processing reservation ${reservation._id}: ${error instanceof Error ? error.message : 'Unknown error'}`;
            console.error(errorMsg);
            errors.push(errorMsg);
          }
        }

        const executionTime = Date.now() - startTime;
        const result = {
          totalReservations: reservations.length,
          smsScheduled,
          skipped,
          errors,
          executionTime,
          nextMondayDate: nextMondayStr,
          branchIds: this.MONDAY_REMINDER_BRANCH_IDS
        };

        // Log execution results
        await this.logCronJobExecution('monday-reservation-reminder', {
          success: true,
          ...result
        });

        console.log(`Monday reservation reminder job completed:`, result);
        return result;
      },
      2 // Max 2 retries
    );
  }

  // Helper method to get next Monday's date
  private static getNextMondayDate(): Date {
    const now = new Date();
    const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday, ..., 6 = Saturday

    let daysToAdd;
    if (dayOfWeek === 6) {
      // If today is Saturday, next Monday is in 2 days
      daysToAdd = 2;
    } else {
      // For other days, calculate days until next Monday
      daysToAdd = (8 - dayOfWeek) % 7;
      if (daysToAdd === 0) daysToAdd = 7; // If today is Monday, get next Monday
    }

    const nextMonday = new Date(now);
    nextMonday.setDate(now.getDate() + daysToAdd);
    return nextMonday;
  }

  // Helper method to get branch data
  private static async getBranchData(branchId: string): Promise<any> {
    try {
      const mongoose = await import('mongoose');
      const branch = await mongoose.connection.collection('branches').findOne(
        { _id: mongoose.Types.ObjectId.createFromHexString(branchId) },
        { projection: { name: 1, address: 1, city: 1, phone: 1, postalCode: 1, province: 1 } }
      );
      return branch;
    } catch (error) {
      console.error(`Error fetching branch data for ${branchId}:`, error);
      return null;
    }
  }

  // Helper method to prepare SMS data
  private static async prepareMondayReminderSMSData(reservation: any, branchData: any): Promise<Record<string, string>> {
    // Format the visit date in French
    const visitDate = new Date(reservation.preferences.visitDate);
    const formattedDate = visitDate.toLocaleDateString('fr-CA', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });

    // Extract visit hour (start time from visitTime like "10:00-12:00")
    const visitTime = reservation.preferences.visitTime || '';
    const visitHour = visitTime.split('-')[0] || '';

    return {
      'customerInfo.client1Name': reservation.customerInfo?.client1Name || '',
      'customerInfo.client2Name': reservation.customerInfo?.client2Name || '',
      'formattedDate': formattedDate,
      'visitHour': visitHour,
      'preferences.visitDate': reservation.preferences.visitDate,
      'preferences.visitTime': reservation.preferences.visitTime,
      'branch.name': branchData?.name || '',
      'branch.address': branchData?.address || '',
      'branch.city': branchData?.city || '',
      'branch.phone': branchData?.phone || '',
      'branch.postalCode': branchData?.postalCode || '',
      'branch.province': branchData?.province || ''
    };
  }

  // Manual trigger method for testing
  static async triggerMondayReservationReminderManually(): Promise<any> {
    console.log('Manually triggering Monday reservation reminder job...');

    try {
      const result = await this.executeMondayReservationReminderJob();

      // Log manual execution
      await this.logCronJobExecution('monday-reservation-reminder-manual', {
        success: true,
        result: result,
        manual: true,
        timestamp: new Date()
      });

      return result;
    } catch (error) {
      console.error('Manual Monday reservation reminder job failed:', error);

      // Log manual execution failure
      await this.logCronJobExecution('monday-reservation-reminder-manual', {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date(),
        manual: true
      });

      throw error;
    }
  }

  // Method to update branch IDs configuration
  static updateMondayReminderBranchIds(branchIds: string[]): void {
    this.MONDAY_REMINDER_BRANCH_IDS = branchIds;
    console.log(`Updated Monday reminder branch IDs: ${branchIds.join(', ')}`);
  }

  // Method to get current branch IDs configuration
  static getMondayReminderBranchIds(): string[] {
    return [...this.MONDAY_REMINDER_BRANCH_IDS];
  }

  // ===== MAIN START METHOD =====

  static async start() {
    console.log('Starting cron scheduler...');

    // Start existing automations
    this.startInvoiceAutomation();

    // Start stats email automations
    await this.startDailyStatsEmailAutomation();
    await this.startWeeklyStatsEmailAutomation();

    // Start new branch stats automation
    await this.startBranchStatsEmailAutomation();

    // Start Monday reservation reminder automation
    this.startMondayReservationReminderAutomation();

    console.log('All cron jobs started successfully');
  }
}

// Helper functions for calculating next run times
function getNextThursday12pm(): Date {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 4 = Thursday

  let daysToAdd;
  if (dayOfWeek < 4 || (dayOfWeek === 4 && now.getHours() < 12)) {
    // Before Thursday 12pm this week
    daysToAdd = 4 - dayOfWeek;
  } else {
    // After Thursday 12pm, get next Thursday
    daysToAdd = 7 - dayOfWeek + 4;
  }

  const nextThursday = new Date(now);
  nextThursday.setDate(now.getDate() + daysToAdd);
  nextThursday.setHours(12, 0, 0, 0);

  return nextThursday;
}

function getNextFriday12pm(): Date {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 5 = Friday

  let daysToAdd;
  if (dayOfWeek < 5 || (dayOfWeek === 5 && now.getHours() < 12)) {
    // Before Friday 12pm this week
    daysToAdd = 5 - dayOfWeek;
  } else {
    // After Friday 12pm, get next Friday
    daysToAdd = 7 - dayOfWeek + 5;
  }

  const nextFriday = new Date(now);
  nextFriday.setDate(now.getDate() + daysToAdd);
  nextFriday.setHours(12, 0, 0, 0);

  return nextFriday;
}

function getNextDaily8am(): Date {
  const now = new Date();
  const next8am = new Date(now);

  if (now.getHours() >= 8) {
    // After 8am today, get tomorrow 8am
    next8am.setDate(now.getDate() + 1);
  }

  next8am.setHours(8, 0, 0, 0);
  return next8am;
}

function getNextDaily2am(): Date {
  const now = new Date();
  const next2am = new Date(now);

  if (now.getHours() >= 2) {
    // After 2am today, get tomorrow 2am
    next2am.setDate(next2am.getDate() + 1);
  }

  next2am.setHours(2, 0, 0, 0);
  return next2am;
}

function getNextMonday9am(): Date {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 1 = Monday

  let daysToAdd;
  if (dayOfWeek === 0) {
    // Sunday, get tomorrow (Monday)
    daysToAdd = 1;
  } else if (dayOfWeek === 1 && now.getHours() < 9) {
    // Before Monday 9am this week
    daysToAdd = 0;
  } else {
    // After Monday 9am, get next Monday
    daysToAdd = 7 - dayOfWeek + 1;
  }

  const nextMonday = new Date(now);
  nextMonday.setDate(now.getDate() + daysToAdd);
  nextMonday.setHours(9, 0, 0, 0);

  return nextMonday;
}

function getNextFriday2359(): Date {
  const now = new Date();
  const dayOfWeek = now.getDay(); // 0 = Sunday, 5 = Friday

  let daysToAdd;
  if (dayOfWeek < 5 || (dayOfWeek === 5 && (now.getHours() < 23 || (now.getHours() === 23 && now.getMinutes() < 59)))) {
    // Before Friday 23:59 this week
    daysToAdd = 5 - dayOfWeek;
  } else {
    // After Friday 23:59, get next Friday
    daysToAdd = 7 - dayOfWeek + 5;
  }

  const nextFriday = new Date(now);
  nextFriday.setDate(now.getDate() + daysToAdd);
  nextFriday.setHours(23, 59, 59, 0);

  return nextFriday;
}
